'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
	Plus,
	MoreHorizontal,
	ArrowLeft,
	Users,
	Calendar,
	MessageSquare,
	Paperclip,
	Eye,
	Star,
	Settings,
	Filter,
	Search,
} from 'lucide-react';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { TaskCard } from './task-card';
import { CreateTaskDialog } from './create-task-dialog';
import { TaskDetailDialog } from './task-detail-dialog';

// Sample kanban data structure
const sampleKanbanData = {
	columns: {
		todo: {
			id: 'todo',
			title: 'To Do',
			color: 'bg-gray-500',
			taskIds: ['task-1', 'task-2', 'task-3'],
		},
		'in-progress': {
			id: 'in-progress',
			title: 'In Progress',
			color: 'bg-blue-500',
			taskIds: ['task-4', 'task-5'],
		},
		review: {
			id: 'review',
			title: 'Review',
			color: 'bg-yellow-500',
			taskIds: ['task-6'],
		},
		done: {
			id: 'done',
			title: 'Done',
			color: 'bg-green-500',
			taskIds: ['task-7', 'task-8'],
		},
	},
	tasks: {
		'task-1': {
			id: 'task-1',
			title: 'Design user authentication flow',
			description:
				'Create wireframes and mockups for the login and registration process',
			coverImage:
				'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=200&fit=crop',
			priority: 'high',
			assignees: [{ id: 1, name: 'John Doe', avatar: null }],
			dueDate: '2024-02-15',
			commentsCount: 3,
			attachmentsCount: 2,
			labels: ['Design', 'UI/UX'],
			createdAt: '2024-01-10',
		},
		'task-2': {
			id: 'task-2',
			title: 'Set up project repository',
			description:
				'Initialize Git repository and set up basic project structure',
			coverImage: null,
			priority: 'medium',
			assignees: [{ id: 2, name: 'Jane Smith', avatar: null }],
			dueDate: '2024-02-10',
			commentsCount: 1,
			attachmentsCount: 0,
			labels: ['Development'],
			createdAt: '2024-01-08',
		},
		'task-3': {
			id: 'task-3',
			title: 'Research competitor analysis',
			description: 'Analyze top 5 competitors and document findings',
			coverImage:
				'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=200&fit=crop',
			priority: 'low',
			assignees: [
				{ id: 3, name: 'Mike Johnson', avatar: null },
				{ id: 4, name: 'Sarah Wilson', avatar: null },
			],
			dueDate: '2024-02-20',
			commentsCount: 5,
			attachmentsCount: 3,
			labels: ['Research', 'Analysis'],
			createdAt: '2024-01-05',
		},
		'task-4': {
			id: 'task-4',
			title: 'Implement user dashboard',
			description: 'Build the main dashboard interface with charts and widgets',
			coverImage:
				'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=200&fit=crop',
			priority: 'high',
			assignees: [{ id: 1, name: 'John Doe', avatar: null }],
			dueDate: '2024-02-18',
			commentsCount: 7,
			attachmentsCount: 1,
			labels: ['Development', 'Frontend'],
			createdAt: '2024-01-12',
		},
		'task-5': {
			id: 'task-5',
			title: 'Database schema design',
			description: 'Design and implement the database structure for user data',
			coverImage: null,
			priority: 'high',
			assignees: [{ id: 5, name: 'Tom Brown', avatar: null }],
			dueDate: '2024-02-16',
			commentsCount: 2,
			attachmentsCount: 4,
			labels: ['Backend', 'Database'],
			createdAt: '2024-01-14',
		},
		'task-6': {
			id: 'task-6',
			title: 'Code review and testing',
			description: 'Review authentication module and write unit tests',
			coverImage:
				'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=200&fit=crop',
			priority: 'medium',
			assignees: [
				{ id: 2, name: 'Jane Smith', avatar: null },
				{ id: 3, name: 'Mike Johnson', avatar: null },
			],
			dueDate: '2024-02-12',
			commentsCount: 4,
			attachmentsCount: 0,
			labels: ['Testing', 'QA'],
			createdAt: '2024-01-16',
		},
		'task-7': {
			id: 'task-7',
			title: 'Project documentation',
			description:
				'Create comprehensive documentation for the project setup and API',
			coverImage: null,
			priority: 'low',
			assignees: [{ id: 4, name: 'Sarah Wilson', avatar: null }],
			dueDate: '2024-02-08',
			commentsCount: 1,
			attachmentsCount: 2,
			labels: ['Documentation'],
			createdAt: '2024-01-03',
		},
		'task-8': {
			id: 'task-8',
			title: 'Initial wireframes',
			description: 'Create basic wireframes for all main pages',
			coverImage:
				'https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=400&h=200&fit=crop',
			priority: 'medium',
			assignees: [
				{ id: 1, name: 'John Doe', avatar: null },
				{ id: 4, name: 'Sarah Wilson', avatar: null },
			],
			dueDate: '2024-02-05',
			commentsCount: 6,
			attachmentsCount: 5,
			labels: ['Design', 'Wireframes'],
			createdAt: '2024-01-01',
		},
	},
	columnOrder: ['todo', 'in-progress', 'review', 'done'],
};

// Sample project data
const sampleProjects = {
	1: {
		id: 1,
		title: 'Website Redesign',
		description: 'Complete overhaul of company website with modern UI/UX',
		color: 'bg-blue-500',
		members: [
			{ id: 1, name: 'John Doe', avatar: null },
			{ id: 2, name: 'Jane Smith', avatar: null },
			{ id: 3, name: 'Mike Johnson', avatar: null },
		],
	},
	2: {
		id: 2,
		title: 'Mobile App Development',
		description: 'Native mobile application for iOS and Android platforms',
		color: 'bg-green-500',
		members: [
			{ id: 4, name: 'Sarah Wilson', avatar: null },
			{ id: 5, name: 'Tom Brown', avatar: null },
		],
	},
};

const KanbanColumn = ({
	column,
	tasks,
	index,
	onTaskClick,
	kanbanData,
	setKanbanData,
}) => {
	return (
		<div className="flex flex-col w-80 bg-muted/30 rounded-lg p-4">
			{/* Column Header */}
			<div className="flex items-center justify-between mb-4">
				<div className="flex items-center gap-2">
					<div className={cn('w-3 h-3 rounded-full', column.color)} />
					<h3 className="font-semibold text-sm">{column.title}</h3>
					<Badge variant="secondary" className="text-xs">
						{tasks.length}
					</Badge>
				</div>
				<div className="flex items-center gap-1">
					<CreateTaskDialog
						columnId={column.id}
						onTaskCreate={(task, columnId) => {
							// Add task to the column
							const newKanbanData = {
								...kanbanData,
								tasks: {
									...kanbanData.tasks,
									[task.id]: task,
								},
								columns: {
									...kanbanData.columns,
									[columnId]: {
										...kanbanData.columns[columnId],
										taskIds: [...kanbanData.columns[columnId].taskIds, task.id],
									},
								},
							};
							setKanbanData(newKanbanData);
						}}
					>
						<Button variant="ghost" size="sm" className="h-6 w-6 p-0">
							<Plus className="h-4 w-4" />
						</Button>
					</CreateTaskDialog>
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button variant="ghost" size="sm" className="h-6 w-6 p-0">
								<MoreHorizontal className="h-4 w-4" />
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align="end">
							<DropdownMenuItem>Edit Column</DropdownMenuItem>
							<DropdownMenuItem>Add Task</DropdownMenuItem>
							<DropdownMenuItem className="text-destructive">
								Delete Column
							</DropdownMenuItem>
						</DropdownMenuContent>
					</DropdownMenu>
				</div>
			</div>

			{/* Tasks List */}
			<Droppable droppableId={column.id}>
				{(provided, snapshot) => (
					<div
						ref={provided.innerRef}
						{...provided.droppableProps}
						className={cn(
							'flex-1 space-y-3 min-h-[200px] transition-colors',
							snapshot.isDraggingOver && 'bg-primary/5 rounded-lg'
						)}
					>
						{tasks.map((task, index) => (
							<Draggable key={task.id} draggableId={task.id} index={index}>
								{(provided, snapshot) => (
									<div
										ref={provided.innerRef}
										{...provided.draggableProps}
										{...provided.dragHandleProps}
										className={cn(
											'transition-all duration-200',
											snapshot.isDragging && 'rotate-2 scale-105 shadow-lg'
										)}
									>
										<TaskCard task={task} onTaskClick={onTaskClick} />
									</div>
								)}
							</Draggable>
						))}
						{provided.placeholder}
					</div>
				)}
			</Droppable>
		</div>
	);
};

export default function ProjectKanbanPage() {
	const params = useParams();
	const router = useRouter();
	const projectId = params.projectId;

	const [kanbanData, setKanbanData] = useState(sampleKanbanData);
	const [project, setProject] = useState(null);
	const [selectedTask, setSelectedTask] = useState(null);
	const [taskDetailOpen, setTaskDetailOpen] = useState(false);
	const [searchQuery, setSearchQuery] = useState('');

	useEffect(() => {
		// In real app, fetch project and kanban data from API
		const projectData = sampleProjects[projectId];
		setProject(projectData);
	}, [projectId]);

	const onDragEnd = (result) => {
		const { destination, source, draggableId } = result;

		if (!destination) return;

		if (
			destination.droppableId === source.droppableId &&
			destination.index === source.index
		) {
			return;
		}

		const start = kanbanData.columns[source.droppableId];
		const finish = kanbanData.columns[destination.droppableId];

		if (start === finish) {
			// Moving within the same column
			const newTaskIds = Array.from(start.taskIds);
			newTaskIds.splice(source.index, 1);
			newTaskIds.splice(destination.index, 0, draggableId);

			const newColumn = {
				...start,
				taskIds: newTaskIds,
			};

			setKanbanData({
				...kanbanData,
				columns: {
					...kanbanData.columns,
					[newColumn.id]: newColumn,
				},
			});
		} else {
			// Moving to a different column
			const startTaskIds = Array.from(start.taskIds);
			startTaskIds.splice(source.index, 1);
			const newStart = {
				...start,
				taskIds: startTaskIds,
			};

			const finishTaskIds = Array.from(finish.taskIds);
			finishTaskIds.splice(destination.index, 0, draggableId);
			const newFinish = {
				...finish,
				taskIds: finishTaskIds,
			};

			setKanbanData({
				...kanbanData,
				columns: {
					...kanbanData.columns,
					[newStart.id]: newStart,
					[newFinish.id]: newFinish,
				},
			});
		}
	};

	const handleTaskClick = (task) => {
		setSelectedTask(task);
		setTaskDetailOpen(true);
	};

	if (!project) {
		return (
			<div className="flex items-center justify-center h-screen">
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
			</div>
		);
	}

	return (
		<div className="flex w-full flex-col bg-background min-h-screen">
			{/* Header */}
			<div className="border-b bg-card">
				<div className="flex items-center justify-between p-4">
					<div className="flex items-center gap-4">
						<Button
							variant="ghost"
							size="sm"
							onClick={() => router.back()}
							className="gap-2"
						>
							<ArrowLeft className="h-4 w-4" />
							Back
						</Button>
						<div className="flex items-center gap-3">
							<div className={cn('w-1 h-8 rounded-full', project.color)} />
							<div>
								<h1 className="text-xl font-bold">{project.title}</h1>
								<p className="text-sm text-muted-foreground">
									{project.description}
								</p>
							</div>
						</div>
					</div>
					<div className="flex items-center gap-3">
						<div className="flex items-center gap-2">
							<Users className="h-4 w-4 text-muted-foreground" />
							<div className="flex -space-x-2">
								{project.members.slice(0, 3).map((member) => (
									<Avatar
										key={member.id}
										className="h-6 w-6 border-2 border-background"
									>
										<AvatarImage src={member.avatar} />
										<AvatarFallback className="text-xs">
											{member.name
												.split(' ')
												.map((n) => n[0])
												.join('')}
										</AvatarFallback>
									</Avatar>
								))}
								{project.members.length > 3 && (
									<div className="h-6 w-6 rounded-full bg-muted border-2 border-background flex items-center justify-center">
										<span className="text-xs font-medium">
											+{project.members.length - 3}
										</span>
									</div>
								)}
							</div>
						</div>
						<Button variant="outline" size="sm">
							<Star className="h-4 w-4 mr-2" />
							Star
						</Button>
						<Button variant="outline" size="sm">
							<Settings className="h-4 w-4 mr-2" />
							Settings
						</Button>
					</div>
				</div>
			</div>

			{/* Toolbar */}
			<div className="border-b bg-card p-4">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-3">
						<div className="relative">
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
							<Input
								placeholder="Search tasks..."
								value={searchQuery}
								onChange={(e) => setSearchQuery(e.target.value)}
								className="pl-9 w-64"
							/>
						</div>
						<Button variant="outline" size="sm">
							<Filter className="h-4 w-4 mr-2" />
							Filter
						</Button>
					</div>
					<div className="flex items-center gap-2">
						<Button variant="outline" size="sm">
							<Eye className="h-4 w-4 mr-2" />
							View Options
						</Button>
						<CreateTaskDialog
							onTaskCreate={(task, columnId = 'todo') => {
								// Add task to the first column by default
								const newKanbanData = {
									...kanbanData,
									tasks: {
										...kanbanData.tasks,
										[task.id]: task,
									},
									columns: {
										...kanbanData.columns,
										[columnId]: {
											...kanbanData.columns[columnId],
											taskIds: [
												...kanbanData.columns[columnId].taskIds,
												task.id,
											],
										},
									},
								};
								setKanbanData(newKanbanData);
							}}
						>
							<Button size="sm">
								<Plus className="h-4 w-4 mr-2" />
								Add Task
							</Button>
						</CreateTaskDialog>
					</div>
				</div>
			</div>

			{/* Kanban Board */}
			<div className="flex-1 p-4">
				<DragDropContext onDragEnd={onDragEnd}>
					<div className="flex gap-6 overflow-x-auto pb-4">
						{kanbanData.columnOrder.map((columnId, index) => {
							const column = kanbanData.columns[columnId];
							const tasks = column.taskIds.map(
								(taskId) => kanbanData.tasks[taskId]
							);

							return (
								<KanbanColumn
									key={column.id}
									column={column}
									tasks={tasks}
									index={index}
									onTaskClick={handleTaskClick}
									kanbanData={kanbanData}
									setKanbanData={setKanbanData}
								/>
							);
						})}

						{/* Add Column Button */}
						<div className="flex-shrink-0">
							<Button variant="outline" className="w-80 h-12 border-dashed">
								<Plus className="h-4 w-4 mr-2" />
								Add Column
							</Button>
						</div>
					</div>
				</DragDropContext>
			</div>

			{/* Task Detail Dialog */}
			<TaskDetailDialog
				task={selectedTask}
				open={taskDetailOpen}
				onOpenChange={setTaskDetailOpen}
				onTaskClick={handleTaskClick}
			/>
		</div>
	);
}
