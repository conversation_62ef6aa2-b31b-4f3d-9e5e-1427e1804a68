import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import React, { useState, useEffect } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, Calendar, Clock, MapPin } from 'lucide-react';
import { format, addDays, subDays, isToday as isTodayFn, startOfDay, isSameDay } from 'date-fns';
import { zonedTimeToUtc, utcToZonedTime } from 'date-fns-tz';

const CalendarCard = () => {
	const [currentDate, setCurrentDate] = useState(new Date());
	const [isFlipping, setIsFlipping] = useState(false);
	const [flipDirection, setFlipDirection] = useState('');
	const [realTimeDate, setRealTimeDate] = useState(new Date());

	// Update real-time date every minute
	useEffect(() => {
		const timer = setInterval(() => {
			setRealTimeDate(new Date());
		}, 60000); // Update every minute

		return () => clearInterval(timer);
	}, []);

	// Auto-navigate to today if we're viewing a past date and it's a new day
	useEffect(() => {
		const now = new Date();
		if (isToday(now) && !isToday(currentDate)) {
			// Optional: Auto-navigate to today when the day changes
			// setCurrentDate(now);
		}
	}, [realTimeDate, currentDate]);

	const handlePrev = () => {
		setIsFlipping(true);
		setFlipDirection('down');
		setTimeout(() => {
			setCurrentDate((prev) => subDays(prev, 1));
			setTimeout(() => setIsFlipping(false), 50);
		}, 150);
	};

	const handleNext = () => {
		setIsFlipping(true);
		setFlipDirection('up');
		setTimeout(() => {
			setCurrentDate((prev) => addDays(prev, 1));
			setTimeout(() => setIsFlipping(false), 50);
		}, 150);
	};

	const isToday = (date) => isTodayFn(date);

	// Real-time calendar events generator
	const generateRealTimeEvents = () => {
		const today = new Date();
		const events = [];

		// Add some recurring work events
		const workDays = [1, 2, 3, 4, 5]; // Monday to Friday
		for (let i = -7; i <= 30; i++) {
			const date = addDays(today, i);
			const dayOfWeek = date.getDay();

			if (workDays.includes(dayOfWeek)) {
				// Daily standup
				events.push({
					date: date,
					title: 'Daily Standup',
					time: '9:00 AM',
					location: 'Conference Room',
					description: 'Daily team standup meeting to discuss progress and blockers.',
					type: 'work'
				});

				// Random meetings
				if (Math.random() > 0.7) {
					events.push({
						date: date,
						title: 'Client Review Meeting',
						time: '2:00 PM',
						location: 'Meeting Room A',
						description: 'Review project progress with client stakeholders.',
						type: 'meeting'
					});
				}
			}
		}

		// Add holidays and special events
		const currentYear = today.getFullYear();
		const currentMonth = today.getMonth();

		// New Year
		if (currentMonth === 0) {
			events.push({
				date: new Date(currentYear, 0, 1),
				title: 'New Year\'s Day',
				time: 'All Day',
				location: 'Global',
				description: 'Happy New Year! Start of a new year with fresh opportunities.',
				type: 'holiday'
			});
		}

		// Christmas
		if (currentMonth === 11) {
			events.push({
				date: new Date(currentYear, 11, 25),
				title: 'Christmas Day',
				time: 'All Day',
				location: 'Global',
				description: 'Merry Christmas! Celebrate with family and friends.',
				type: 'holiday'
			});
		}

		// Add some random social events
		for (let i = 0; i < 5; i++) {
			const randomDay = addDays(today, Math.floor(Math.random() * 14) - 7);
			if (randomDay.getDay() === 5) { // Friday
				events.push({
					date: randomDay,
					title: 'Team Happy Hour',
					time: '6:00 PM',
					location: 'Local Pub',
					description: 'Weekly team bonding session over drinks and snacks.',
					type: 'social'
				});
			}
		}

		// Today's special events
		if (isToday(today)) {
			events.push({
				date: today,
				title: 'Project Deadline',
				time: '5:00 PM',
				location: 'Office',
				description: 'Final submission deadline for the quarterly project deliverables.',
				type: 'work'
			});
		}

		return events;
	};

	const events = generateRealTimeEvents();

	const getEvents = (date) => {
		// Filter events that match the current date
		return events.filter(
			(event) =>
				event.date.getDate() === date.getDate() &&
				event.date.getMonth() === date.getMonth() &&
				event.date.getFullYear() === date.getFullYear()
		);
	};

	const eventsForToday = getEvents(currentDate);

	const getEventTypeColor = (type) => {
		switch (type) {
			case 'holiday': return 'bg-red-100 text-red-700 border-red-200';
			case 'meeting': return 'bg-blue-100 text-blue-700 border-blue-200';
			case 'social': return 'bg-green-100 text-green-700 border-green-200';
			case 'work': return 'bg-purple-100 text-purple-700 border-purple-200';
			default: return 'bg-gray-100 text-gray-700 border-gray-200';
		}
	};

	return (
		<Card className="min-w-[280px] max-w-full p-4 space-y-4">
			{/* Header with navigation */}
			<div className="flex items-center justify-between">
				<h3 className="text-lg font-semibold text-card-foreground flex items-center gap-2">
					<Calendar className="h-5 w-5" />
					Calendar
				</h3>
				<div className="flex items-center gap-2">
					<Button
						variant="outline"
						size="sm"
						onClick={handlePrev}
						className="h-8 w-8 p-0"
					>
						<ChevronLeftIcon className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="sm"
						onClick={handleNext}
						className="h-8 w-8 p-0"
					>
						<ChevronRightIcon className="h-4 w-4" />
					</Button>
				</div>
			</div>

			{/* Physical Calendar Design */}
			<div className="relative h-48 perspective-1000">
				<div
					className={`absolute inset-0 w-full h-full ${
						isFlipping
							? flipDirection === 'up'
								? 'calendar-flip-up'
								: 'calendar-flip-down'
							: ''
					}`}
				>
					{/* Calendar Front */}
					<div className="absolute inset-0 w-full h-full backface-hidden">
						{/* Calendar Base (Dark Frame) */}
						<div className="w-full h-full bg-slate-800 rounded-2xl p-3 shadow-2xl">
							{/* Calendar Pages Stack Effect */}
							<div className="relative">
								{/* Bottom pages (stack effect) */}
								<div className="absolute top-1 left-1 right-1 h-full bg-gray-200 rounded-xl"></div>
								<div className="absolute top-0.5 left-0.5 right-0.5 h-full bg-gray-300 rounded-xl"></div>

								{/* Top Calendar Page */}
								<Dialog>
									<DialogTrigger asChild>
										<div className="relative bg-white rounded-xl shadow-lg cursor-pointer hover:shadow-xl transition-shadow duration-200 overflow-hidden">
											{/* Red Header Bar */}
											<div className="bg-red-500 h-8 w-full flex items-center justify-center">
												<div className="text-white text-sm font-medium">
													{format(currentDate, 'MMMM yyyy')}
												</div>
											</div>

											{/* Calendar Content */}
											<div className="p-4 text-center">
												{/* Day of Week */}
												<div className="text-xs font-medium text-gray-600 mb-1">
													{format(currentDate, 'EEEE')}
												</div>

												{/* Large Date Number */}
												<div className="text-4xl font-bold text-red-500 mb-2">
													{format(currentDate, 'dd')}
												</div>

												{/* Today Badge */}
												{isToday(currentDate) && (
													<div className="inline-block px-2 py-1 bg-red-500 text-white text-xs rounded-full mb-2">
														Today
													</div>
												)}

												{/* Events Summary */}
												<div className="space-y-1">
													{eventsForToday.length > 0 ? (
														<>
															<div className="text-xs text-gray-600">
																{eventsForToday.length} event{eventsForToday.length > 1 ? 's' : ''}
															</div>
															{eventsForToday.slice(0, 2).map((event, index) => (
																<div
																	key={index}
																	className="text-xs text-gray-700 truncate"
																>
																	• {event.title}
																</div>
															))}
															{eventsForToday.length > 2 && (
																<div className="text-xs text-gray-500">
																	+{eventsForToday.length - 2} more
																</div>
															)}
														</>
													) : (
														<div className="text-xs text-gray-400">
															No events
														</div>
													)}
												</div>
											</div>
										</div>
									</DialogTrigger>

									{/* Event Details Dialog */}
									<DialogContent className="max-w-md">
										<DialogHeader>
											<DialogTitle className="flex items-center gap-2">
												<Calendar className="h-5 w-5" />
												{format(currentDate, 'EEEE, MMMM dd, yyyy')}
											</DialogTitle>
										</DialogHeader>

										<div className="space-y-4">
											{eventsForToday.length > 0 ? (
												eventsForToday.map((event, index) => (
													<div
														key={index}
														className={`p-4 rounded-lg border ${getEventTypeColor(event.type)}`}
													>
														<div className="flex items-start justify-between mb-2">
															<h4 className="font-semibold">{event.title}</h4>
															<span className="text-xs px-2 py-1 bg-white rounded-full capitalize">
																{event.type}
															</span>
														</div>

														<div className="space-y-2 text-sm">
															<div className="flex items-center gap-2">
																<Clock className="h-4 w-4" />
																<span>{event.time}</span>
															</div>
															<div className="flex items-center gap-2">
																<MapPin className="h-4 w-4" />
																<span>{event.location}</span>
															</div>
															<p className="text-gray-600 mt-2">
																{event.description}
															</p>
														</div>
													</div>
												))
											) : (
												<div className="text-center py-8 text-gray-500">
													<Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
													<p>No events scheduled for this day</p>
												</div>
											)}
										</div>
									</DialogContent>
								</Dialog>
							</div>
						</div>
					</div>

					{/* Calendar Back (for flip effect) */}
					<div className="absolute inset-0 w-full h-full backface-hidden rotate-y-180 bg-slate-800 rounded-2xl p-3 shadow-2xl">
						<div className="flex items-center justify-center h-full text-gray-400">
							<Calendar className="h-16 w-16" />
						</div>
					</div>
				</div>
			</div>
		</Card>
	);
};

export default CalendarCard;
