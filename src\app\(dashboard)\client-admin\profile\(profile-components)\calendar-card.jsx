import React, { useState } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, Calendar, Clock, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { format, addDays, subDays, isToday } from 'date-fns';

const CalendarCard = () => {
	const today = new Date();
	const [currentDate, setCurrentDate] = useState(today);
	const [isFlipping, setIsFlipping] = useState(false);
	const [nextDate, setNextDate] = useState(addDays(today, 1));

	const handleFlip = () => {
		if (isFlipping) return;

		setIsFlipping(true);

		// Calculate next date
		const next = addDays(currentDate, 1);
		setNextDate(next);

		// After animation completes, update current date
		setTimeout(() => {
			setCurrentDate(next);
			setIsFlipping(false);
		}, 800); // Match animation duration
	};

	const handlePrev = () => {
		if (isFlipping) return;

		setIsFlipping(true);

		// Calculate previous date
		const prev = subDays(currentDate, 1);
		setNextDate(prev);

		// After animation completes, update current date
		setTimeout(() => {
			setCurrentDate(prev);
			setIsFlipping(false);
		}, 800);
	};

	const handleNext = () => {
		if (isFlipping) return;

		setIsFlipping(true);

		// Calculate next date
		const next = addDays(currentDate, 1);
		setNextDate(next);

		// After animation completes, update current date
		setTimeout(() => {
			setCurrentDate(next);
			setIsFlipping(false);
		}, 800);
	};

	// Sample events data
	const events = [
		{
			date: today,
			title: 'Team Standup',
			time: '9:00 AM',
			location: 'Conference Room A',
			description: 'Daily team standup meeting to discuss progress and blockers.',
			type: 'work'
		},
		{
			date: today,
			title: 'Project Review',
			time: '2:00 PM',
			location: 'Meeting Room B',
			description: 'Quarterly project review with stakeholders.',
			type: 'meeting'
		},
		{
			date: today,
			title: 'Design Workshop',
			time: '11:00 AM',
			location: 'Creative Studio',
			description: 'Collaborative design workshop for the new product features.',
			type: 'work'
		},
		{
			date: today,
			title: 'Client Call',
			time: '4:00 PM',
			location: 'Video Conference',
			description: 'Weekly check-in call with the client to discuss project status.',
			type: 'meeting'
		},
		{
			date: today,
			title: 'Team Happy Hour',
			time: '6:00 PM',
			location: 'Local Pub',
			description: 'End of week celebration with the team.',
			type: 'social'
		},
		{
			date: today,
			title: 'Documentation Update',
			time: '1:00 PM',
			location: 'Office',
			description: 'Update project documentation and user guides.',
			type: 'work'
		},
		{
			date: addDays(today, 1),
			title: 'Client Presentation',
			time: '10:00 AM',
			location: 'Main Hall',
			description: 'Present project deliverables to the client.',
			type: 'meeting'
		},
		{
			date: addDays(today, 1),
			title: 'Team Lunch',
			time: '12:30 PM',
			location: 'Restaurant',
			description: 'Monthly team lunch and bonding session.',
			type: 'social'
		},
		{
			date: subDays(today, 1),
			title: 'Code Review',
			time: '3:00 PM',
			location: 'Dev Room',
			description: 'Weekly code review session.',
			type: 'work'
		}
	];

	// Get events for the current date
	const getEventsForDate = (date) => {
		return events.filter(event =>
			event.date.toDateString() === date.toDateString()
		);
	};

	const currentEvents = getEventsForDate(currentDate);

	const getEventTypeColor = (type) => {
		switch (type) {
			case 'work': return 'bg-purple-100 text-purple-700 border-purple-200';
			case 'meeting': return 'bg-blue-100 text-blue-700 border-blue-200';
			case 'social': return 'bg-green-100 text-green-700 border-green-200';
			case 'holiday': return 'bg-red-100 text-red-700 border-red-200';
			default: return 'bg-gray-100 text-gray-700 border-gray-200';
		}
	};

	// Optional: Auto-flip every 5 seconds for demo (disabled by default)
	// useEffect(() => {
	// 	const interval = setInterval(() => {
	// 		handleNext();
	// 	}, 5000);
	//
	// 	return () => clearInterval(interval);
	// }, [currentDate, isFlipping]);



	return (
		<div className="min-w-[280px] max-w-full p-4 space-y-4">
			{/* Header with title and navigation */}
			<div className="flex items-center justify-between">
				<h3 className="text-lg font-semibold text-card-foreground flex items-center gap-2">
					<Calendar className="h-5 w-5" />
					Calendar
				</h3>
				<div className="flex items-center gap-2">
					<Button
						variant="outline"
						size="sm"
						onClick={handlePrev}
						className="h-8 w-8 p-0"
						disabled={isFlipping}
					>
						<ChevronLeftIcon className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="sm"
						onClick={handleNext}
						className="h-8 w-8 p-0"
						disabled={isFlipping}
					>
						<ChevronRightIcon className="h-4 w-4" />
					</Button>
				</div>
			</div>

			{/* Two Column Layout */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Left Column - Calendar */}
				<div className="flex items-center justify-center min-h-[300px] bg-gradient-to-br from-blue-100 to-blue-200 p-6 rounded-lg">
					<div className="relative">
						{/* Calendar Container */}
						<div
							className="calendar-container cursor-pointer"
							onClick={handleFlip}
						>
							{/* Calendar Base - Stack of papers */}
							<div className="calendar-base">
								{/* Bottom sheets for 3D effect */}
								<div className="calendar-sheet calendar-sheet-3"></div>
								<div className="calendar-sheet calendar-sheet-2"></div>
								<div className="calendar-sheet calendar-sheet-1"></div>

								{/* Top flipping page */}
								<div className={`calendar-page ${isFlipping ? 'flipping' : ''}`}>
									{/* Front of the page */}
									<div className="page-front">
										<div className="date-number">{format(currentDate, 'dd')}</div>
									</div>

									{/* Back of the page (shows during flip) */}
									<div className="page-back">
										<div className="date-number">{format(nextDate, 'dd')}</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* Right Column - Events */}
				<div className="h-[300px] bg-white rounded-lg border border-gray-200 p-4 flex flex-col">
					{/* Date Header - Fixed */}
					<div className="mb-4 pb-3 border-b border-gray-200 flex-shrink-0">
						<h4 className="text-lg font-semibold text-gray-900">
							{format(currentDate, 'EEEE, MMMM dd, yyyy')}
						</h4>
						{isToday(currentDate) && (
							<span className="inline-block mt-1 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
								Today
							</span>
						)}
					</div>

					{/* Events List - Scrollable */}
					<div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
						<div className="space-y-3 pr-2">
							{currentEvents.length > 0 ? (
								currentEvents.map((event, index) => (
									<div
										key={index}
										className={`p-3 rounded-lg border ${getEventTypeColor(event.type)}`}
									>
										<div className="flex items-start justify-between mb-2">
											<h5 className="font-semibold text-sm">{event.title}</h5>
											<span className="text-xs px-2 py-1 bg-white rounded-full capitalize">
												{event.type}
											</span>
										</div>

										<div className="space-y-1 text-xs">
											<div className="flex items-center gap-2">
												<Clock className="h-3 w-3" />
												<span>{event.time}</span>
											</div>
											<div className="flex items-center gap-2">
												<MapPin className="h-3 w-3" />
												<span>{event.location}</span>
											</div>
											<p className="text-gray-600 mt-2 text-xs">
												{event.description}
											</p>
										</div>
									</div>
								))
							) : (
								<div className="text-center py-8 text-gray-500">
									<Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
									<p className="text-sm">No events scheduled</p>
									<p className="text-xs text-gray-400">Enjoy your free day!</p>
								</div>
							)}
						</div>
					</div>
				</div>
			</div>

			{/* Styles */}
			<style jsx>{`
				.calendar-container {
					perspective: 1000px;
					perspective-origin: center top;
				}

				.calendar-base {
					position: relative;
					width: 200px;
					height: 240px;
					filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.15));
				}

				.calendar-sheet {
					position: absolute;
					width: 100%;
					height: 100%;
					background: linear-gradient(145deg, #ffffff, #f8f9fa);
					border-radius: 12px;
					border: 1px solid rgba(0, 0, 0, 0.1);
				}

				.calendar-sheet-3 {
					transform: translateY(6px) translateX(3px);
					background: linear-gradient(145deg, #f1f3f4, #e8eaed);
					z-index: 1;
				}

				.calendar-sheet-2 {
					transform: translateY(3px) translateX(1.5px);
					background: linear-gradient(145deg, #f8f9fa, #f1f3f4);
					z-index: 2;
				}

				.calendar-sheet-1 {
					transform: translateY(1px) translateX(0.5px);
					background: linear-gradient(145deg, #ffffff, #f8f9fa);
					z-index: 3;
				}

				.calendar-page {
					position: absolute;
					top: 0;
					left: 0;
					width: 100%;
					height: 100%;
					transform-style: preserve-3d;
					transform-origin: center top;
					transition: transform 0.8s cubic-bezier(0.4, 0.0, 0.2, 1);
					z-index: 4;
				}

				.calendar-page.flipping {
					transform: rotateX(-180deg);
				}

				.page-front,
				.page-back {
					position: absolute;
					width: 100%;
					height: 100%;
					backface-visibility: hidden;
					background: linear-gradient(145deg, #ffffff, #f8f9fa);
					border-radius: 12px;
					border: 1px solid rgba(0, 0, 0, 0.1);
					display: flex;
					align-items: center;
					justify-content: center;
					box-shadow:
						0 1px 3px rgba(0, 0, 0, 0.1),
						0 4px 12px rgba(0, 0, 0, 0.05);
				}

				.page-back {
					transform: rotateX(180deg);
					background: linear-gradient(145deg, #f8f9fa, #ffffff);
				}

				.date-number {
					font-size: 4rem;
					font-weight: 900;
					color: #dc2626;
					text-shadow:
						0 2px 4px rgba(220, 38, 38, 0.2),
						0 4px 8px rgba(220, 38, 38, 0.1);
					user-select: none;
				}

				/* Hover effect */
				.calendar-container:hover .calendar-page {
					transform: rotateX(-5deg);
				}

				.calendar-container:hover .calendar-page.flipping {
					transform: rotateX(-180deg);
				}

				/* Enhanced shadows during flip */
				.calendar-page.flipping .page-front,
				.calendar-page.flipping .page-back {
					box-shadow:
						0 8px 25px rgba(0, 0, 0, 0.2),
						0 16px 40px rgba(0, 0, 0, 0.1);
				}

				/* Subtle curl effect */
				.calendar-sheet::before {
					content: '';
					position: absolute;
					top: 0;
					right: 0;
					width: 20px;
					height: 20px;
					background: linear-gradient(135deg, transparent 45%, rgba(0, 0, 0, 0.05) 50%, rgba(0, 0, 0, 0.02) 55%, transparent 65%);
					border-radius: 0 12px 0 0;
				}

				.page-front::before,
				.page-back::before {
					content: '';
					position: absolute;
					top: 0;
					right: 0;
					width: 20px;
					height: 20px;
					background: linear-gradient(135deg, transparent 45%, rgba(0, 0, 0, 0.05) 50%, rgba(0, 0, 0, 0.02) 55%, transparent 65%);
					border-radius: 0 12px 0 0;
				}
			`}</style>
		</div>
	);
};

export default CalendarCard;
