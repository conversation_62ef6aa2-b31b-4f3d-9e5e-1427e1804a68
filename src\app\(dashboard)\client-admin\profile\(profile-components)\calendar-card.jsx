import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import React, { useState } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, Calendar } from 'lucide-react';
import { format, addDays, subDays, isToday as isTodayFn } from 'date-fns';

const CalendarCard = () => {
	const [currentDate, setCurrentDate] = useState(new Date());
	const [isFlipping, setIsFlipping] = useState(false);
	const [flipDirection, setFlipDirection] = useState('');

	const handlePrev = () => {
		setIsFlipping(true);
		setFlipDirection('prev');
		setTimeout(() => {
			setCurrentDate((prev) => subDays(prev, 1));
			setTimeout(() => setIsFlipping(false), 50);
		}, 150);
	};

	const handleNext = () => {
		setIsFlipping(true);
		setFlipDirection('next');
		setTimeout(() => {
			setCurrentDate((prev) => addDays(prev, 1));
			setTimeout(() => setIsFlipping(false), 50);
		}, 150);
	};

	const isToday = (date) => isTodayFn(date);

	// Dummy events data
	const events = [
		{ date: new Date(), title: 'Diwali' },
		{ date: addDays(new Date(), 1), title: 'Meeting with Client' },
		{ date: subDays(new Date(), 1), title: 'Team Outing' },
		{ date: addDays(new Date(), 3), title: 'Code Review' },
	];

	const getEvents = (date) => {
		// Filter events that match the current date
		return events.filter(
			(event) =>
				event.date.getDate() === date.getDate() &&
				event.date.getMonth() === date.getMonth() &&
				event.date.getFullYear() === date.getFullYear()
		);
	};

	const eventsForToday = getEvents(currentDate);

	return (
		<Card className="min-w-[280px] max-w-full p-4 space-y-4">
			{/* Header with navigation */}
			<div className="flex items-center justify-between">
				<h3 className="text-lg font-semibold text-card-foreground flex items-center gap-2">
					<Calendar className="h-5 w-5" />
					Calendar
				</h3>
				<div className="flex items-center gap-2">
					<Button
						variant="outline"
						size="sm"
						onClick={handlePrev}
						className="h-8 w-8 p-0"
					>
						<ChevronLeftIcon className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="sm"
						onClick={handleNext}
						className="h-8 w-8 p-0"
					>
						<ChevronRightIcon className="h-4 w-4" />
					</Button>
				</div>
			</div>

			{/* Flipping Calendar Page */}
			<div className="relative h-48 perspective-1000">
				<div
					className={`absolute inset-0 w-full h-full transition-transform duration-300 transform-style-preserve-3d ${
						isFlipping
							? flipDirection === 'next'
								? 'rotate-y-180'
								: 'rotate-y-neg-180'
							: 'rotate-y-0'
					}`}
				>
					{/* Front of the calendar page */}
					<div className="absolute inset-0 w-full h-full backface-hidden bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg border border-blue-200 p-4">
						{/* Date Display */}
						<div className="text-center mb-4">
							<div className="text-3xl font-bold text-blue-900">
								{format(currentDate, 'dd')}
							</div>
							<div className="text-sm font-medium text-blue-700">
								{format(currentDate, 'MMM yyyy')}
							</div>
							<div className="text-xs text-blue-600">
								{format(currentDate, 'EEEE')}
							</div>
							{isToday(currentDate) && (
								<div className="inline-block mt-2 px-2 py-1 bg-blue-500 text-white text-xs rounded-full">
									Today
								</div>
							)}
						</div>

						{/* Events Section */}
						<div className="space-y-2">
							<div className="text-sm font-medium text-gray-700 border-b border-gray-200 pb-1">
								Events
							</div>
							{eventsForToday.length > 0 ? (
								<div className="space-y-2 max-h-20 overflow-y-auto">
									{eventsForToday.map((event, index) => (
										<div
											key={index}
											className="text-xs bg-white rounded-md p-2 border border-gray-200 shadow-sm"
										>
											<div className="font-medium text-gray-900">{event.title}</div>
										</div>
									))}
								</div>
							) : (
								<div className="text-xs text-gray-500 italic text-center py-4">
									No events scheduled
								</div>
							)}
						</div>
					</div>

					{/* Back of the calendar page (for flip effect) */}
					<div className="absolute inset-0 w-full h-full backface-hidden rotate-y-180 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border border-gray-200 p-4">
						<div className="flex items-center justify-center h-full text-gray-400">
							<Calendar className="h-12 w-12" />
						</div>
					</div>
				</div>
			</div>
		</Card>
	);
};

export default CalendarCard;
