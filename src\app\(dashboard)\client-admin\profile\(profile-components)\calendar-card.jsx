import React, { useState, useEffect } from 'react';

const CalendarCard = () => {
	const [currentDate, setCurrentDate] = useState(31);
	const [isFlipping, setIsFlipping] = useState(false);
	const [nextDate, setNextDate] = useState(1);

	const handleFlip = () => {
		if (isFlipping) return;

		setIsFlipping(true);

		// Calculate next date
		const next = currentDate >= 31 ? 1 : currentDate + 1;
		setNextDate(next);

		// After animation completes, update current date
		setTimeout(() => {
			setCurrentDate(next);
			setIsFlipping(false);
		}, 800); // Match animation duration
	};

	// Auto-flip every 3 seconds for demo (optional)
	useEffect(() => {
		const interval = setInterval(() => {
			handleFlip();
		}, 3000);

		return () => clearInterval(interval);
	}, [currentDate, isFlipping]);



	return (
		<div className="flex items-center justify-center min-h-[300px] bg-gradient-to-br from-blue-100 to-blue-200 p-8 rounded-lg">
			<div className="relative">
				{/* Calendar Container */}
				<div
					className="calendar-container cursor-pointer"
					onClick={handleFlip}
				>
					{/* Calendar Base - Stack of papers */}
					<div className="calendar-base">
						{/* Bottom sheets for 3D effect */}
						<div className="calendar-sheet calendar-sheet-3"></div>
						<div className="calendar-sheet calendar-sheet-2"></div>
						<div className="calendar-sheet calendar-sheet-1"></div>

						{/* Top flipping page */}
						<div className={`calendar-page ${isFlipping ? 'flipping' : ''}`}>
							{/* Front of the page */}
							<div className="page-front">
								<div className="date-number">{currentDate}</div>
							</div>

							{/* Back of the page (shows during flip) */}
							<div className="page-back">
								<div className="date-number">{nextDate}</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Styles */}
			<style jsx>{`
				.calendar-container {
					perspective: 1000px;
					perspective-origin: center top;
				}

				.calendar-base {
					position: relative;
					width: 200px;
					height: 240px;
					filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.15));
				}

				.calendar-sheet {
					position: absolute;
					width: 100%;
					height: 100%;
					background: linear-gradient(145deg, #ffffff, #f8f9fa);
					border-radius: 12px;
					border: 1px solid rgba(0, 0, 0, 0.1);
				}

				.calendar-sheet-3 {
					transform: translateY(6px) translateX(3px);
					background: linear-gradient(145deg, #f1f3f4, #e8eaed);
					z-index: 1;
				}

				.calendar-sheet-2 {
					transform: translateY(3px) translateX(1.5px);
					background: linear-gradient(145deg, #f8f9fa, #f1f3f4);
					z-index: 2;
				}

				.calendar-sheet-1 {
					transform: translateY(1px) translateX(0.5px);
					background: linear-gradient(145deg, #ffffff, #f8f9fa);
					z-index: 3;
				}

				.calendar-page {
					position: absolute;
					top: 0;
					left: 0;
					width: 100%;
					height: 100%;
					transform-style: preserve-3d;
					transform-origin: center top;
					transition: transform 0.8s cubic-bezier(0.4, 0.0, 0.2, 1);
					z-index: 4;
				}

				.calendar-page.flipping {
					transform: rotateX(-180deg);
				}

				.page-front,
				.page-back {
					position: absolute;
					width: 100%;
					height: 100%;
					backface-visibility: hidden;
					background: linear-gradient(145deg, #ffffff, #f8f9fa);
					border-radius: 12px;
					border: 1px solid rgba(0, 0, 0, 0.1);
					display: flex;
					align-items: center;
					justify-content: center;
					box-shadow:
						0 1px 3px rgba(0, 0, 0, 0.1),
						0 4px 12px rgba(0, 0, 0, 0.05);
				}

				.page-back {
					transform: rotateX(180deg);
					background: linear-gradient(145deg, #f8f9fa, #ffffff);
				}

				.date-number {
					font-size: 4rem;
					font-weight: 900;
					color: #dc2626;
					text-shadow:
						0 2px 4px rgba(220, 38, 38, 0.2),
						0 4px 8px rgba(220, 38, 38, 0.1);
					user-select: none;
				}

				/* Hover effect */
				.calendar-container:hover .calendar-page {
					transform: rotateX(-5deg);
				}

				.calendar-container:hover .calendar-page.flipping {
					transform: rotateX(-180deg);
				}

				/* Enhanced shadows during flip */
				.calendar-page.flipping .page-front,
				.calendar-page.flipping .page-back {
					box-shadow:
						0 8px 25px rgba(0, 0, 0, 0.2),
						0 16px 40px rgba(0, 0, 0, 0.1);
				}

				/* Subtle curl effect */
				.calendar-sheet::before {
					content: '';
					position: absolute;
					top: 0;
					right: 0;
					width: 20px;
					height: 20px;
					background: linear-gradient(135deg, transparent 45%, rgba(0, 0, 0, 0.05) 50%, rgba(0, 0, 0, 0.02) 55%, transparent 65%);
					border-radius: 0 12px 0 0;
				}

				.page-front::before,
				.page-back::before {
					content: '';
					position: absolute;
					top: 0;
					right: 0;
					width: 20px;
					height: 20px;
					background: linear-gradient(135deg, transparent 45%, rgba(0, 0, 0, 0.05) 50%, rgba(0, 0, 0, 0.02) 55%, transparent 65%);
					border-radius: 0 12px 0 0;
				}
			`}</style>
		</div>
	);
};

export default CalendarCard;
