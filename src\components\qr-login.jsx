'use client';

import { useEffect, useState, useRef } from 'react';
import { io } from 'socket.io-client';
import QRCodeStyling from 'qr-code-styling';
import { Wifi, WifiOff, RotateCcw, RefreshCw } from 'lucide-react';
import { useAppDispatch } from '../lib/hooks';
import { loginAfterQrScan } from '../lib/features/auth/authSlice';
import dayjs from 'dayjs';
import { Button } from './ui/button';

// const socket = io('http://localhost:5000/auth', {
// 	withCredentials: true,
// }); // Localhost

const socket = io('https://tms-backend-muzr.onrender.com/auth', {
	withCredentials: true,
}); // staging

// const socket = io('https://harp-hr-backend.onrender.com/auth', {
// 	withCredentials: true,
// }); // production

const qrCode = new QRCodeStyling({
	width: 160,
	height: 160,
	type: 'png',
	image:
		'https://res.cloudinary.com/dv8nbs25p/image/upload/v1744395029/HarpHr/cousgbm3q0vxbdisz8g7.png',
	dotsOptions: {
		color: '#225850',
		type: 'extra-rounded',
		size: 8,
	},
	backgroundOptions: {
		color: '#fff',
		margin: 0.5,
	},
	imageOptions: {
		crossOrigin: 'anonymous',
		margin: 0.5,
		imageSize: 0.4,
	},
	cornersSquareOptions: {
		type: 'extra-rounded',
		color: '#fbb309',
	},
	cornersDotOptions: {
		type: 'dot',
		color: '#225850',
	},
});

const QrLogin = () => {
	const ref = useRef(null);
	const dispatch = useAppDispatch();

	const [sessionId, setSessionId] = useState('');
	const [connectionStatus, setConnectionStatus] = useState('connecting');
	const [lastUpdated, setLastUpdated] = useState(null);
	const [expiresAt, setExpiresAt] = useState(null);
	const [countdown, setCountdown] = useState(150); // in seconds

	// Handle countdown
	useEffect(() => {
		if (connectionStatus !== 'connected' || !expiresAt) return;

		const interval = setInterval(() => {
			const remaining = expiresAt.diff(dayjs(), 'second');
			setCountdown(remaining);

			if (remaining <= 0) {
				socket.disconnect();
				setConnectionStatus('timed-out');
				// Clear the QR code from the DOM
				if (ref.current) {
					ref.current.innerHTML = '';
				}

				clearInterval(interval);
				clearInterval(interval);
			}
		}, 1000);

		return () => clearInterval(interval);
	}, [expiresAt, connectionStatus]);

	// Socket connection & listeners
	useEffect(() => {
		socket.on('connect', () => {
			setConnectionStatus('connected');
			const now = dayjs();
			setExpiresAt(now.add(60, 'second'));
			setCountdown(60);
			setSessionId(socket.id);
		});

		socket.on('disconnect', () => {
			setConnectionStatus('disconnected');
		});

		socket.on('qr-session', (data) => {
			const qrUrl = data.qrSessionId;
			const now = dayjs();
			setLastUpdated(now);
			qrCode.update({ data: qrUrl });

			if (ref.current) {
				ref.current.innerHTML = '';
				qrCode.append(ref.current);
			}
		});

		socket.on('login-successful', (data) => {
			dispatch(loginAfterQrScan(data));
		});

		return () => {
			socket.off('connect');
			socket.off('disconnect');
			socket.off('qr-session');
			socket.off('login-successful');
		};
	}, [dispatch, setExpiresAt]);

	const refreshQr = () => {
		socket.connect();
		setConnectionStatus('connecting');
	};

	const getStatusIcon = () => {
		switch (connectionStatus) {
			case 'connected':
				return <Wifi className="h-3 w-3 text-green-500" />;
			case 'connecting':
				return <RotateCcw className="h-3 w-3 text-yellow-500 animate-spin" />;
			case 'disconnected':
			case 'timed-out':
				return <WifiOff className="h-3 w-3 text-red-500" />;
			default:
				return <WifiOff className="h-3 w-3 text-gray-400" />;
		}
	};

	const getStatusText = () => {
		switch (connectionStatus) {
			case 'connected':
				return 'Connected';
			case 'connecting':
				return 'Connecting...';
			case 'disconnected':
				return 'Disconnected';
			case 'timed-out':
				return 'Session timed out';
			default:
				return 'Unknown';
		}
	};

	return (
		<div className="flex flex-col items-center space-y-3">
			{/* QR Code */}
			<div
				ref={ref}
				className="flex items-center justify-center w-40 h-40 bg-white rounded-lg border border-gray-200 shadow-sm"
			/>

			{/* Connection Status */}
			<div className="flex items-center space-x-2 text-xs">
				{getStatusIcon()}
				<span
					className={`${
						connectionStatus === 'connected'
							? 'text-green-600'
							: connectionStatus === 'connecting'
								? 'text-yellow-600'
								: 'text-red-600'
					}`}
				>
					{getStatusText()}
				</span>
			</div>

			{/* Countdown Timer */}
			{connectionStatus === 'connected' && expiresAt && (
				<div className="text-xs text-gray-500">
					Session expires in: {countdown}s
				</div>
			)}

			{/* Session Info */}
			{sessionId && (
				<div className="text-center space-y-1">
					<div className="text-xs text-gray-500">
						Session ID: {sessionId.slice(-6).toLowerCase()}
					</div>
					{lastUpdated && (
						<div className="text-xs text-gray-500">
							Last updated: {lastUpdated.format('HH:mm:ss')}
						</div>
					)}
				</div>
			)}

			{/* Refresh Button */}
			{connectionStatus === 'timed-out' && (
				<Button onClick={refreshQr} size="icon" variant="outline">
					<RefreshCw />
				</Button>
			)}
		</div>
	);
};

export default QrLogin;
