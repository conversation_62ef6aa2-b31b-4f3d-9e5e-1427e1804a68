'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
	Di<PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
	Calendar,
	MessageSquare,
	Paperclip,
	Send,
	Edit,
	Trash2,
	Star,
	Clock,
	AlertTriangle,
	CheckCircle2,
	User,
	Tag,
	Image as ImageIcon,
	X,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Image from 'next/image';

// Sample comments data
const sampleComments = [
	{
		id: 1,
		author: { id: 1, name: '<PERSON>', avatar: null },
		content:
			'I think we should focus on the mobile-first approach for this design. The current wireframes look great but we need to ensure they work well on smaller screens.',
		timestamp: '2024-01-15T10:30:00Z',
		edited: false,
	},
	{
		id: 2,
		author: { id: 2, name: 'Jane <PERSON>', avatar: null },
		content:
			"Agreed! I've already started working on the responsive breakpoints. Should have the mobile version ready by tomorrow.",
		timestamp: '2024-01-15T11:15:00Z',
		edited: false,
	},
	{
		id: 3,
		author: { id: 3, name: 'Mike Johnson', avatar: null },
		content:
			'Great work everyone! Just a quick note - make sure to follow our design system guidelines for spacing and typography.',
		timestamp: '2024-01-15T14:20:00Z',
		edited: true,
	},
];

export function TaskDetailDialog({ task, open, onOpenChange }) {
	const [comments, setComments] = useState(sampleComments);
	const [newComment, setNewComment] = useState('');
	const [imageError, setImageError] = useState(false);

	if (!task) return null;

	const getPriorityColor = (priority) => {
		switch (priority) {
			case 'high':
				return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
			case 'medium':
				return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
			case 'low':
				return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
			default:
				return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
		}
	};

	const getPriorityIcon = (priority) => {
		switch (priority) {
			case 'high':
				return <AlertTriangle className="h-4 w-4" />;
			case 'medium':
				return <Clock className="h-4 w-4" />;
			case 'low':
				return <CheckCircle2 className="h-4 w-4" />;
			default:
				return null;
		}
	};

	const handleAddComment = () => {
		if (newComment.trim()) {
			const comment = {
				id: Date.now(),
				author: { id: 1, name: 'Current User', avatar: null }, // In real app, get from auth
				content: newComment.trim(),
				timestamp: new Date().toISOString(),
				edited: false,
			};
			setComments([...comments, comment]);
			setNewComment('');
		}
	};

	const formatTimestamp = (timestamp) => {
		const date = new Date(timestamp);
		const now = new Date();
		const diffInHours = (now - date) / (1000 * 60 * 60);

		if (diffInHours < 1) {
			return 'Just now';
		} else if (diffInHours < 24) {
			return `${Math.floor(diffInHours)}h ago`;
		} else {
			return date.toLocaleDateString();
		}
	};

	const isOverdue = task.dueDate && new Date(task.dueDate) < new Date();
	const isDueSoon =
		task.dueDate &&
		new Date(task.dueDate) <= new Date(Date.now() + 3 * 24 * 60 * 60 * 1000);

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[1000px] max-h-[90vh] p-0">
				{/* Header */}
				<DialogHeader className="p-6 pb-0">
					<div className="flex items-start justify-between">
						<div className="flex-1">
							<DialogTitle className="text-xl font-bold mb-2">
								{task.title}
							</DialogTitle>
							<div className="flex items-center gap-2 flex-wrap">
								{/* Priority Badge */}
								<Badge
									variant="secondary"
									className={cn(
										'flex items-center gap-1',
										getPriorityColor(task.priority)
									)}
								>
									{getPriorityIcon(task.priority)}
									{task.priority} priority
								</Badge>

								{/* Labels */}
								{task.labels?.map((label, index) => (
									<Badge key={index} variant="outline" className="text-xs">
										<Tag className="h-3 w-3 mr-1" />
										{label}
									</Badge>
								))}

								{/* Due Date */}
								{task.dueDate && (
									<Badge
										variant="outline"
										className={cn(
											'flex items-center gap-1',
											isOverdue
												? 'border-red-500 text-red-600 dark:text-red-400'
												: isDueSoon
													? 'border-yellow-500 text-yellow-600 dark:text-yellow-400'
													: ''
										)}
									>
										<Calendar className="h-3 w-3" />
										{isOverdue
											? 'Overdue'
											: isDueSoon
												? 'Due soon'
												: 'Due'}: {new Date(task.dueDate).toLocaleDateString()}
									</Badge>
								)}
							</div>
						</div>
						<div className="flex items-center gap-2">
							<Button variant="ghost" size="sm">
								<Star className="h-4 w-4" />
							</Button>
							<Button variant="outline" size="sm">
								<Edit className="h-4 w-4 mr-2" />
								Edit
							</Button>
						</div>
					</div>
				</DialogHeader>

				{/* Cover Image */}
				{task.coverImage && !imageError && (
					<div className="relative w-full h-48 mx-6 rounded-lg overflow-hidden">
						<Image
							src={task.coverImage}
							alt={task.title}
							fill
							className="object-cover"
							onError={() => setImageError(true)}
						/>
					</div>
				)}

				{/* Main Content - Two Column Layout */}
				<div className="flex flex-1 min-h-0">
					{/* Left Column - Description and Details */}
					<div className="flex-1 p-6 pt-4">
						<ScrollArea className="h-[400px]">
							<div className="space-y-6">
								{/* Description */}
								<div>
									<h3 className="font-semibold mb-3 flex items-center gap-2">
										<Edit className="h-4 w-4" />
										Description
									</h3>
									<div className="prose prose-sm max-w-none">
										<p className="text-muted-foreground leading-relaxed">
											{task.description || 'No description provided.'}
										</p>
									</div>
								</div>

								<Separator />

								{/* Assignees */}
								<div>
									<h3 className="font-semibold mb-3 flex items-center gap-2">
										<User className="h-4 w-4" />
										Assignees ({task.assignees?.length || 0})
									</h3>
									<div className="flex flex-wrap gap-2">
										{task.assignees?.map((assignee) => (
											<div
												key={assignee.id}
												className="flex items-center gap-2 p-2 rounded-lg border bg-card"
											>
												<Avatar className="h-8 w-8">
													<AvatarImage src={assignee.avatar} />
													<AvatarFallback className="text-xs">
														{assignee.name
															.split(' ')
															.map((n) => n[0])
															.join('')}
													</AvatarFallback>
												</Avatar>
												<span className="text-sm font-medium">
													{assignee.name}
												</span>
											</div>
										))}
										<Button variant="outline" size="sm" className="h-12">
											<User className="h-4 w-4 mr-2" />
											Add Assignee
										</Button>
									</div>
								</div>

								<Separator />

								{/* Attachments */}
								<div>
									<h3 className="font-semibold mb-3 flex items-center gap-2">
										<Paperclip className="h-4 w-4" />
										Attachments ({task.attachmentsCount || 0})
									</h3>
									{task.attachmentsCount > 0 ? (
										<div className="space-y-2">
											{/* Sample attachments */}
											<div className="flex items-center gap-3 p-3 rounded-lg border bg-card">
												<div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded flex items-center justify-center">
													<ImageIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
												</div>
												<div className="flex-1">
													<div className="font-medium text-sm">
														design-mockup.png
													</div>
													<div className="text-xs text-muted-foreground">
														2.4 MB • Added 2 days ago
													</div>
												</div>
												<Button variant="ghost" size="sm">
													<Trash2 className="h-4 w-4" />
												</Button>
											</div>
										</div>
									) : (
										<div className="text-center py-8 text-muted-foreground">
											<Paperclip className="h-8 w-8 mx-auto mb-2 opacity-50" />
											<p className="text-sm">No attachments yet</p>
										</div>
									)}
									<Button variant="outline" size="sm" className="mt-3">
										<Paperclip className="h-4 w-4 mr-2" />
										Add Attachment
									</Button>
								</div>
							</div>
						</ScrollArea>
					</div>

					{/* Right Column - Comments */}
					<div className="w-80 border-l bg-muted/20 flex flex-col">
						{/* Comments Header */}
						<div className="p-4 border-b">
							<h3 className="font-semibold flex items-center gap-2">
								<MessageSquare className="h-4 w-4" />
								Comments ({comments.length})
							</h3>
						</div>

						{/* Comments List */}
						<ScrollArea className="flex-1 p-4">
							<div className="space-y-4">
								{comments.map((comment) => (
									<div key={comment.id} className="space-y-2">
										<div className="flex items-start gap-3">
											<Avatar className="h-8 w-8 mt-0.5">
												<AvatarImage src={comment.author.avatar} />
												<AvatarFallback className="text-xs">
													{comment.author.name
														.split(' ')
														.map((n) => n[0])
														.join('')}
												</AvatarFallback>
											</Avatar>
											<div className="flex-1 min-w-0">
												<div className="flex items-center gap-2 mb-1">
													<span className="font-medium text-sm">
														{comment.author.name}
													</span>
													<span className="text-xs text-muted-foreground">
														{formatTimestamp(comment.timestamp)}
													</span>
													{comment.edited && (
														<span className="text-xs text-muted-foreground">
															(edited)
														</span>
													)}
												</div>
												<div className="text-sm leading-relaxed">
													{comment.content}
												</div>
											</div>
										</div>
									</div>
								))}
							</div>
						</ScrollArea>

						{/* Add Comment */}
						<div className="p-4 border-t">
							<div className="space-y-3">
								<Textarea
									placeholder="Write a comment..."
									value={newComment}
									onChange={(e) => setNewComment(e.target.value)}
									className="min-h-[80px] resize-none"
									onKeyDown={(e) => {
										if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
											e.preventDefault();
											handleAddComment();
										}
									}}
								/>
								<div className="flex justify-between items-center">
									<span className="text-xs text-muted-foreground">
										Press Cmd+Enter to send
									</span>
									<Button
										size="sm"
										onClick={handleAddComment}
										disabled={!newComment.trim()}
									>
										<Send className="h-4 w-4 mr-2" />
										Send
									</Button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
