'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
	Plus,
	MoreHorizontal,
	Users,
	Calendar,
	Star,
	Clock,
	CheckCircle2,
	AlertCircle,
	Folder,
	Settings,
} from 'lucide-react';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { CreateProjectDialog } from './create-project-dialog';
import { ProjectDetailDialog } from './project-detail-dialog';

// Sample project data
const sampleProjects = [
	{
		id: 1,
		title: 'Website Redesign',
		description: 'Complete overhaul of company website with modern UI/UX',
		status: 'active',
		priority: 'high',
		dueDate: '2024-02-15',
		progress: 65,
		members: [
			{ id: 1, name: '<PERSON>', avatar: null },
			{ id: 2, name: '<PERSON>', avatar: null },
			{ id: 3, name: '<PERSON> <PERSON>', avatar: null },
		],
		tasksCount: 24,
		completedTasks: 16,
		color: 'bg-blue-500',
		isStarred: true,
	},
	{
		id: 2,
		title: 'Mobile App Development',
		description: 'Native mobile application for iOS and Android platforms',
		status: 'active',
		priority: 'medium',
		dueDate: '2024-03-20',
		progress: 30,
		members: [
			{ id: 4, name: 'Sarah Wilson', avatar: null },
			{ id: 5, name: 'Tom Brown', avatar: null },
		],
		tasksCount: 18,
		completedTasks: 5,
		color: 'bg-green-500',
		isStarred: false,
	},
	{
		id: 3,
		title: 'Database Migration',
		description: 'Migrate legacy database to new cloud infrastructure',
		status: 'completed',
		priority: 'high',
		dueDate: '2024-01-30',
		progress: 100,
		members: [
			{ id: 6, name: 'Alex Chen', avatar: null },
			{ id: 7, name: 'Lisa Park', avatar: null },
		],
		tasksCount: 12,
		completedTasks: 12,
		color: 'bg-purple-500',
		isStarred: true,
	},
	{
		id: 4,
		title: 'Marketing Campaign',
		description: 'Q1 digital marketing campaign across all channels',
		status: 'planning',
		priority: 'low',
		dueDate: '2024-04-10',
		progress: 10,
		members: [{ id: 8, name: 'Emma Davis', avatar: null }],
		tasksCount: 8,
		completedTasks: 1,
		color: 'bg-orange-500',
		isStarred: false,
	},
];

const ProjectCard = ({ project, onClick }) => {
	const getStatusIcon = (status) => {
		switch (status) {
			case 'completed':
				return <CheckCircle2 className="h-4 w-4 text-green-600" />;
			case 'active':
				return <Clock className="h-4 w-4 text-blue-600" />;
			case 'planning':
				return <AlertCircle className="h-4 w-4 text-orange-600" />;
			default:
				return <Folder className="h-4 w-4 text-muted-foreground" />;
		}
	};

	const getPriorityColor = (priority) => {
		switch (priority) {
			case 'high':
				return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
			case 'medium':
				return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
			case 'low':
				return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
			default:
				return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
		}
	};

	return (
		<Card
			className="group cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-[1.02]"
			onClick={() => onClick(project)}
		>
			<CardHeader className="pb-3">
				<div className="flex items-start justify-between">
					<div className={cn('w-1 h-8 rounded-full mr-3', project.color)} />
					<div className="flex-1">
						<div className="flex items-center justify-between mb-2">
							<CardTitle className="text-lg font-semibold line-clamp-1">
								{project.title}
							</CardTitle>
							<div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
								<Button
									variant="ghost"
									size="sm"
									className="h-8 w-8 p-0"
									onClick={(e) => {
										e.stopPropagation();
										// Handle star toggle
									}}
								>
									<Star
										className={cn(
											'h-4 w-4',
											project.isStarred
												? 'fill-yellow-400 text-yellow-400'
												: 'text-muted-foreground'
										)}
									/>
								</Button>
								<DropdownMenu>
									<DropdownMenuTrigger asChild>
										<Button
											variant="ghost"
											size="sm"
											className="h-8 w-8 p-0"
											onClick={(e) => e.stopPropagation()}
										>
											<MoreHorizontal className="h-4 w-4" />
										</Button>
									</DropdownMenuTrigger>
									<DropdownMenuContent align="end">
										<DropdownMenuItem>
											<Settings className="h-4 w-4 mr-2" />
											Edit Project
										</DropdownMenuItem>
										<DropdownMenuItem>
											<Users className="h-4 w-4 mr-2" />
											Manage Members
										</DropdownMenuItem>
										<DropdownMenuItem className="text-destructive">
											Delete Project
										</DropdownMenuItem>
									</DropdownMenuContent>
								</DropdownMenu>
							</div>
						</div>
						<p className="text-sm text-muted-foreground line-clamp-2 mb-3">
							{project.description}
						</p>
					</div>
				</div>
			</CardHeader>
			<CardContent className="pt-0">
				<div className="space-y-4">
					{/* Status and Priority */}
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-2">
							{getStatusIcon(project.status)}
							<span className="text-sm font-medium capitalize">
								{project.status}
							</span>
						</div>
						<Badge
							variant="secondary"
							className={getPriorityColor(project.priority)}
						>
							{project.priority}
						</Badge>
					</div>

					{/* Progress Bar */}
					<div className="space-y-2">
						<div className="flex justify-between text-sm">
							<span className="text-muted-foreground">Progress</span>
							<span className="font-medium">{project.progress}%</span>
						</div>
						<div className="w-full bg-muted rounded-full h-2">
							<div
								className={cn(
									'h-2 rounded-full transition-all duration-300',
									project.color
								)}
								style={{ width: `${project.progress}%` }}
							/>
						</div>
					</div>

					{/* Tasks Count */}
					<div className="flex items-center justify-between text-sm">
						<div className="flex items-center gap-2 text-muted-foreground">
							<CheckCircle2 className="h-4 w-4" />
							<span>
								{project.completedTasks}/{project.tasksCount} tasks
							</span>
						</div>
						<div className="flex items-center gap-2 text-muted-foreground">
							<Calendar className="h-4 w-4" />
							<span>{new Date(project.dueDate).toLocaleDateString()}</span>
						</div>
					</div>

					{/* Team Members */}
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-2">
							<Users className="h-4 w-4 text-muted-foreground" />
							<span className="text-sm text-muted-foreground">Team</span>
						</div>
						<div className="flex -space-x-2">
							{project.members.slice(0, 3).map((member, index) => (
								<Avatar
									key={member.id}
									className="h-6 w-6 border-2 border-background"
								>
									<AvatarImage src={member.avatar} />
									<AvatarFallback className="text-xs">
										{member.name
											.split(' ')
											.map((n) => n[0])
											.join('')}
									</AvatarFallback>
								</Avatar>
							))}
							{project.members.length > 3 && (
								<div className="h-6 w-6 rounded-full bg-muted border-2 border-background flex items-center justify-center">
									<span className="text-xs font-medium">
										+{project.members.length - 3}
									</span>
								</div>
							)}
						</div>
					</div>
				</div>
			</CardContent>
		</Card>
	);
};

const CreateProjectCard = () => {
	return (
		<Card className="group cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-[1.02] border-dashed border-2 hover:border-primary/50">
			<CardContent className="flex flex-col items-center justify-center py-12 px-6">
				<div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4 group-hover:bg-primary/20 transition-colors">
					<Plus className="h-8 w-8 text-primary" />
				</div>
				<h3 className="text-lg font-semibold mb-2">Create New Project</h3>
				<p className="text-sm text-muted-foreground text-center">
					Start a new project and organize your tasks efficiently
				</p>
			</CardContent>
		</Card>
	);
};

export default function ProjectsKanbanPage() {
	const [projects, setProjects] = useState(sampleProjects);
	const [selectedProject, setSelectedProject] = useState(null);
	const [projectDetailOpen, setProjectDetailOpen] = useState(false);

	const handleProjectClick = (project) => {
		setSelectedProject(project);
		setProjectDetailOpen(true);
		console.log('Selected project:', project);
	};

	const handleCreateProject = (newProject) => {
		setProjects([...projects, newProject]);
		console.log('Created new project:', newProject);
	};

	return (
		<div className="flex w-full flex-col bg-background">
			<div className="flex flex-col">
				<main className="flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
					{/* Header */}
					<div className="flex items-center justify-between">
						<div>
							<h1 className="text-3xl font-bold tracking-tight">Projects</h1>
							<p className="text-muted-foreground">
								Manage and track your projects efficiently
							</p>
						</div>
						<CreateProjectDialog onProjectCreate={handleCreateProject}>
							<Button className="gap-2">
								<Plus className="h-4 w-4" />
								New Project
							</Button>
						</CreateProjectDialog>
					</div>

					{/* Projects Grid */}
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
						{/* Create New Project Card */}
						<CreateProjectDialog onProjectCreate={handleCreateProject}>
							<div>
								<CreateProjectCard />
							</div>
						</CreateProjectDialog>

						{/* Project Cards */}
						{projects.map((project) => (
							<ProjectCard
								key={project.id}
								project={project}
								onClick={handleProjectClick}
							/>
						))}
					</div>

					{/* Project Detail Dialog */}
					<ProjectDetailDialog
						project={selectedProject}
						open={projectDetailOpen}
						onOpenChange={setProjectDetailOpen}
					/>
				</main>
			</div>
		</div>
	);
}
