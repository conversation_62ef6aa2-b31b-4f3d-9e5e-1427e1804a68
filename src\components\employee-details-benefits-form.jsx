import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import {
	Edit,
	Loader2,
	Save,
	X,
	Shield,
	Calendar,
	Clock,
	Gift,
	Users,
	MapPin,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import * as z from 'zod';

// Benefits schema
const benefitsDetailsSchema = z.object({
	isEligibleForOffInLieu: z.boolean().default(false),
	holidayGroups: z.array(z.any()).optional(),
	leaveGroups: z.array(z.any()).optional(),
});

const EmployeeDetailsBenefitsForm = ({ employeeId, benefits }) => {
	const dispatch = useAppDispatch();
	const { isLoading } = useAppSelector((store) => store.employee);

	const [isEditing, setIsEditing] = useState(false);
	const [originalFormValues, setOriginalFormValues] = useState(null);

	const form = useForm({
		resolver: zodResolver(benefitsDetailsSchema),
		defaultValues: {
			isEligibleForOffInLieu: benefits?.isEligibleForOffInLieu || false,
			holidayGroups: benefits?.holidayGroups || [],
			leaveGroups: benefits?.leaveGroups || [],
		},
	});

	// Sample data for demonstration
	const sampleHolidayGroups = [
		{
			id: 1,
			name: 'National Holidays',
			description: 'Official national holidays and observances',
			totalDays: 12,
			holidays: [
				{
					id: 1,
					name: "New Year's Day",
					date: '2024-01-01',
					type: 'National',
					isOptional: false,
				},
				{
					id: 2,
					name: 'Independence Day',
					date: '2024-07-04',
					type: 'National',
					isOptional: false,
				},
				{
					id: 3,
					name: 'Christmas Day',
					date: '2024-12-25',
					type: 'National',
					isOptional: false,
				},
				{
					id: 4,
					name: 'Thanksgiving',
					date: '2024-11-28',
					type: 'National',
					isOptional: false,
				},
			],
		},
		{
			id: 2,
			name: 'Religious Holidays',
			description: 'Optional religious observances',
			totalDays: 8,
			holidays: [
				{
					id: 5,
					name: 'Diwali',
					date: '2024-11-01',
					type: 'Religious',
					isOptional: true,
				},
				{
					id: 6,
					name: 'Eid al-Fitr',
					date: '2024-04-10',
					type: 'Religious',
					isOptional: true,
				},
				{
					id: 7,
					name: 'Chinese New Year',
					date: '2024-02-10',
					type: 'Religious',
					isOptional: true,
				},
			],
		},
	];

	const sampleLeaveGroups = [
		{
			id: 1,
			name: 'Annual Leave',
			description: 'Yearly vacation and personal time off',
			totalDays: 25,
			usedDays: 8,
			remainingDays: 17,
			leaveTypes: [
				{
					id: 1,
					name: 'Vacation Leave',
					allowedDays: 20,
					usedDays: 6,
					carryForward: true,
				},
				{
					id: 2,
					name: 'Personal Leave',
					allowedDays: 5,
					usedDays: 2,
					carryForward: false,
				},
			],
		},
		{
			id: 2,
			name: 'Medical Leave',
			description: 'Health-related time off and medical appointments',
			totalDays: 15,
			usedDays: 3,
			remainingDays: 12,
			leaveTypes: [
				{
					id: 3,
					name: 'Sick Leave',
					allowedDays: 10,
					usedDays: 2,
					carryForward: false,
				},
				{
					id: 4,
					name: 'Medical Appointments',
					allowedDays: 5,
					usedDays: 1,
					carryForward: false,
				},
			],
		},
		{
			id: 3,
			name: 'Special Leave',
			description: 'Maternity, paternity, and emergency leave',
			totalDays: 30,
			usedDays: 0,
			remainingDays: 30,
			leaveTypes: [
				{
					id: 5,
					name: 'Maternity Leave',
					allowedDays: 16,
					usedDays: 0,
					carryForward: false,
				},
				{
					id: 6,
					name: 'Paternity Leave',
					allowedDays: 7,
					usedDays: 0,
					carryForward: false,
				},
				{
					id: 7,
					name: 'Emergency Leave',
					allowedDays: 7,
					usedDays: 0,
					carryForward: false,
				},
			],
		},
	];

	const onSubmit = async (data) => {
		console.log('Benefits form data to submit:', data);

		// TODO: Implement the actual API call
		// const result = await dispatch(
		// 	updateEmployeeDetailsBenefits({
		// 		employeeId,
		// 		...data,
		// 	})
		// );

		// if (updateEmployeeDetailsBenefits.fulfilled.match(result)) {
		// 	setIsEditing(false);
		// }

		// For now, just simulate success
		setTimeout(() => {
			setIsEditing(false);
		}, 1000);
	};

	const formatDate = (dateString) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			weekday: 'short',
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	};

	const getHolidayTypeColor = (type) => {
		switch (type) {
			case 'National':
				return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
			case 'Religious':
				return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400';
			default:
				return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
		}
	};

	const getLeaveProgressColor = (percentage) => {
		if (percentage >= 80) return 'bg-red-500';
		if (percentage >= 60) return 'bg-yellow-500';
		return 'bg-green-500';
	};

	return (
		<>
			{/* Edit Controls */}
			<div className="flex justify-end mb-4">
				<div className="flex gap-2">
					{isEditing && (
						<Button
							className="bg-red-600 text-white"
							variant="outline"
							onClick={() => {
								// Reset form to original values
								if (originalFormValues) {
									form.reset(originalFormValues);
								}
								setIsEditing(false);
							}}
							disabled={isLoading}
						>
							<X className="h-4 w-4 mr-2" size={16} />
							Cancel
						</Button>
					)}
					<Button
						variant="default"
						onClick={() => {
							if (isEditing) {
								form.handleSubmit(onSubmit)();
							} else {
								// Save original form values before entering edit mode
								const currentValues = form.getValues();
								setOriginalFormValues(currentValues);
								setIsEditing(true);
							}
						}}
						disabled={isLoading}
					>
						{isLoading ? (
							<Loader2 className="animate-spin mr-2" size={16} />
						) : isEditing ? (
							<Save className="h-4 w-4 mr-2" size={16} />
						) : (
							<Edit className="h-4 w-4 mr-2" size={16} />
						)}
						{isEditing ? 'Save' : 'Edit'}
					</Button>
				</div>
			</div>

			<div className="grid grid-cols-1 gap-6">
				{/* Benefits Configuration Card */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="flex items-center gap-2">
							<Shield className="h-5 w-5" />
							Benefits Configuration
						</CardTitle>
					</CardHeader>
					<CardContent>
						{isEditing ? (
							<Form {...form}>
								<div className="space-y-6">
									<FormField
										control={form.control}
										name="isEligibleForOffInLieu"
										render={({ field }) => (
											<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
												<div className="space-y-0.5">
													<FormLabel className="text-sm font-medium text-muted-foreground">
														Eligible for Off in Lieu
													</FormLabel>
													<FormDescription>
														Allow time off in lieu of overtime compensation
													</FormDescription>
												</div>
												<FormControl>
													<Switch
														checked={field.value}
														onCheckedChange={field.onChange}
													/>
												</FormControl>
											</FormItem>
										)}
									/>
								</div>
							</Form>
						) : (
							<div className="space-y-4">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Eligible for Off in Lieu
									</p>
									<p className="text-sm">
										{benefits?.isEligibleForOffInLieu ? 'Yes' : 'No'}
									</p>
								</div>
							</div>
						)}
					</CardContent>
				</Card>

				{/* Holiday Groups Card */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="flex items-center gap-2">
							<Calendar className="h-5 w-5" />
							Holiday Groups
						</CardTitle>
					</CardHeader>
					<CardContent>
						<Accordion type="single" collapsible className="w-full">
							{sampleHolidayGroups.map((group) => (
								<AccordionItem key={group.id} value={`holiday-${group.id}`}>
									<AccordionTrigger className="hover:no-underline">
										<div className="flex items-center justify-between w-full mr-4">
											<div className="flex items-center gap-3">
												<div className="flex items-center gap-2">
													<Gift className="h-4 w-4 text-primary" />
													<span className="font-medium">{group.name}</span>
												</div>
												<Badge variant="outline" className="text-xs">
													{group.totalDays} days
												</Badge>
											</div>
											<p className="text-sm text-muted-foreground text-left">
												{group.description}
											</p>
										</div>
									</AccordionTrigger>
									<AccordionContent>
										<div className="space-y-3 pt-2">
											<div className="grid gap-3">
												{group.holidays.map((holiday) => (
													<div
														key={holiday.id}
														className="flex items-center justify-between p-3 rounded-lg border bg-card"
													>
														<div className="flex items-center gap-3">
															<Calendar className="h-4 w-4 text-muted-foreground" />
															<div>
																<p className="font-medium text-sm">
																	{holiday.name}
																</p>
																<p className="text-xs text-muted-foreground">
																	{formatDate(holiday.date)}
																</p>
															</div>
														</div>
														<div className="flex items-center gap-2">
															<Badge
																variant="secondary"
																className={cn(
																	'text-xs',
																	getHolidayTypeColor(holiday.type)
																)}
															>
																{holiday.type}
															</Badge>
															{holiday.isOptional && (
																<Badge variant="outline" className="text-xs">
																	Optional
																</Badge>
															)}
														</div>
													</div>
												))}
											</div>
										</div>
									</AccordionContent>
								</AccordionItem>
							))}
						</Accordion>
					</CardContent>
				</Card>

				{/* Leave Groups Card */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="flex items-center gap-2">
							<Clock className="h-5 w-5" />
							Leave Groups
						</CardTitle>
					</CardHeader>
					<CardContent>
						<Accordion type="single" collapsible className="w-full">
							{sampleLeaveGroups.map((group) => (
								<AccordionItem key={group.id} value={`leave-${group.id}`}>
									<AccordionTrigger className="hover:no-underline">
										<div className="flex items-center justify-between w-full mr-4">
											<div className="flex items-center gap-3">
												<div className="flex items-center gap-2">
													<Users className="h-4 w-4 text-primary" />
													<span className="font-medium">{group.name}</span>
												</div>
												<div className="flex items-center gap-2">
													<Badge variant="outline" className="text-xs">
														{group.remainingDays}/{group.totalDays} days left
													</Badge>
													<div className="w-16 h-2 bg-muted rounded-full overflow-hidden">
														<div
															className={cn(
																'h-full transition-all duration-300',
																getLeaveProgressColor(
																	(group.usedDays / group.totalDays) * 100
																)
															)}
															style={{
																width: `${(group.usedDays / group.totalDays) * 100}%`,
															}}
														/>
													</div>
												</div>
											</div>
											<p className="text-sm text-muted-foreground text-left">
												{group.description}
											</p>
										</div>
									</AccordionTrigger>
									<AccordionContent>
										<div className="space-y-3 pt-2">
											<div className="grid gap-3">
												{group.leaveTypes.map((leaveType) => (
													<div
														key={leaveType.id}
														className="flex items-center justify-between p-3 rounded-lg border bg-card"
													>
														<div className="flex items-center gap-3">
															<MapPin className="h-4 w-4 text-muted-foreground" />
															<div>
																<p className="font-medium text-sm">
																	{leaveType.name}
																</p>
																<p className="text-xs text-muted-foreground">
																	{leaveType.usedDays} of{' '}
																	{leaveType.allowedDays} days used
																</p>
															</div>
														</div>
														<div className="flex items-center gap-2">
															<div className="w-20 h-2 bg-muted rounded-full overflow-hidden">
																<div
																	className={cn(
																		'h-full transition-all duration-300',
																		getLeaveProgressColor(
																			(leaveType.usedDays /
																				leaveType.allowedDays) *
																				100
																		)
																	)}
																	style={{
																		width: `${(leaveType.usedDays / leaveType.allowedDays) * 100}%`,
																	}}
																/>
															</div>
															{leaveType.carryForward && (
																<Badge variant="outline" className="text-xs">
																	Carry Forward
																</Badge>
															)}
														</div>
													</div>
												))}
											</div>
										</div>
									</AccordionContent>
								</AccordionItem>
							))}
						</Accordion>
					</CardContent>
				</Card>
			</div>
		</>
	);
};

export default EmployeeDetailsBenefitsForm;
