import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import {
	Edit,
	Loader2,
	Save,
	X,
	Shield,
	Calendar,
	Clock,
	Gift,
	Users,
	MapPin,
	Pointer,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { updateEmployeeDetailsBenefit } from '@/lib/features/employees/updateEmployeeSlice';
import * as z from 'zod';
import { icons } from '@/data/icons';

// Benefits schema
const benefitsDetailsSchema = z.object({
	isEligibleForOffInLieu: z.boolean().default(false),
	holidayGroups: z
		.array(z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid ObjectId format'))
		.optional(),
	leaveGroups: z
		.array(z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid ObjectId format'))
		.optional(),
});

const EditEmployeeBenefitDetailsForm = ({ employeeId, benefits }) => {
	const dispatch = useAppDispatch();
	const { isLoading } = useAppSelector((store) => store.updateEmployee);

	const [isEditing, setIsEditing] = useState(false);
	const [originalFormValues, setOriginalFormValues] = useState(null);
	console.log(benefits);
	const form = useForm({
		resolver: zodResolver(benefitsDetailsSchema),
		defaultValues: {
			isEligibleForOffInLieu: benefits?.isEligibleForOffInLieu || false,
			holidayGroups:
				benefits?.holidayGroups?.map((group) => group._id || group.id) || [],
			leaveGroups:
				benefits?.leaveGroups?.map((group) => group._id || group.id) || [],
		},
	});

	// Update form when benefits prop changes
	// React.useEffect(() => {
	// 	if (benefits) {
	// 		form.reset({
	// 			isEligibleForOffInLieu: benefits.isEligibleForOffInLieu || false,
	// 			holidayGroups:
	// 				benefits.holidayGroups?.map((group) => group._id || group.id) || [],
	// 			leaveGroups:
	// 				benefits.leaveGroups?.map((group) => group._id || group.id) || [],
	// 		});
	// 	}
	// }, [benefits, form]);

	const onSubmit = async (data) => {
		console.log('Benefits form data to submit:', data);

		const result = await dispatch(
			updateEmployeeDetailsBenefit({
				employeeId,
				...data,
			})
		);

		if (updateEmployeeDetailsBenefit.fulfilled.match(result)) {
			setIsEditing(false);
		}
	};

	const formatDate = (dateString) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			weekday: 'short',
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	};

	const getHolidayTypeColor = (type) => {
		switch (type?.toLowerCase()) {
			case 'national':
				return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
			case 'religious':
				return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400';
			case 'company':
				return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
			default:
				return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
		}
	};

	const getLeaveProgressColor = (percentage) => {
		if (percentage >= 80) return 'bg-red-500';
		if (percentage >= 60) return 'bg-yellow-500';
		return 'bg-green-500';
	};

	return (
		<>
			{/* Edit Controls */}
			<div className="flex justify-end mb-4">
				<div className="flex gap-2">
					{isEditing && (
						<Button
							className="bg-red-600 text-white"
							variant="outline"
							onClick={() => {
								// Reset form to original values
								if (originalFormValues) {
									form.reset(originalFormValues);
								}
								setIsEditing(false);
							}}
							disabled={isLoading}
						>
							<X className="h-4 w-4 mr-2" size={16} />
							Cancel
						</Button>
					)}
					<Button
						variant="default"
						onClick={() => {
							if (isEditing) {
								form.handleSubmit(onSubmit)();
							} else {
								// Save original form values before entering edit mode
								const currentValues = form.getValues();
								setOriginalFormValues(currentValues);
								setIsEditing(true);
							}
						}}
						disabled={isLoading}
					>
						{isLoading ? (
							<Loader2 className="animate-spin mr-2" size={16} />
						) : isEditing ? (
							<Save className="h-4 w-4 mr-2" size={16} />
						) : (
							<Edit className="h-4 w-4 mr-2" size={16} />
						)}
						{isEditing ? 'Save' : 'Edit'}
					</Button>
				</div>
			</div>

			<div className="grid grid-cols-1 gap-6">
				{/* Benefits Configuration Card */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="flex items-center gap-2">
							<Shield className="h-5 w-5" />
							Benefits Configuration
						</CardTitle>
					</CardHeader>
					<CardContent>
						{isEditing ? (
							<Form {...form}>
								<div className="space-y-6">
									<FormField
										control={form.control}
										name="isEligibleForOffInLieu"
										render={({ field }) => (
											<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
												<div className="space-y-0.5">
													<FormLabel className="text-sm font-medium text-muted-foreground">
														Eligible for Off in Lieu
													</FormLabel>
													<FormDescription>
														Allow time off in lieu of overtime compensation
													</FormDescription>
												</div>
												<FormControl>
													<Switch
														checked={field.value}
														onCheckedChange={field.onChange}
													/>
												</FormControl>
											</FormItem>
										)}
									/>
								</div>
							</Form>
						) : (
							<div className="space-y-4">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Eligible for Off in Lieu
									</p>
									<p className="text-sm">
										{benefits?.isEligibleForOffInLieu ? 'Yes' : 'No'}
									</p>
								</div>
							</div>
						)}
					</CardContent>
				</Card>

				{/* Holiday Groups Card */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="flex items-center gap-2">
							<Calendar className="h-5 w-5" />
							Holiday Groups
						</CardTitle>
					</CardHeader>
					<CardContent>
						{!benefits?.holidayGroups || benefits.holidayGroups.length === 0 ? (
							<div className="text-center py-8 text-muted-foreground">
								<Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
								<p className="text-sm">No holiday groups assigned</p>
								{isEditing && (
									<p className="text-xs mt-1">
										Contact HR to assign holiday groups
									</p>
								)}
							</div>
						) : (
							<Accordion type="single" collapsible className="w-full">
								{benefits.holidayGroups.map((group) => (
									<AccordionItem
										key={group._id || group.id}
										value={`holiday-${group._id || group.id}`}
									>
										<AccordionTrigger className="hover:no-underline">
											<div className="flex items-center justify-between w-full mr-4">
												<div className="flex items-center gap-3">
													<div className="flex items-center gap-2">
														<Gift className="h-4 w-4 text-primary" />
														<span className="font-medium">{group.name}</span>
													</div>
													{group.holidays && (
														<Badge variant="outline" className="text-xs">
															{group.holidays.length} holidays
														</Badge>
													)}
												</div>
												{group.description && (
													<p className="text-sm text-muted-foreground text-left">
														{group.description}
													</p>
												)}
											</div>
										</AccordionTrigger>
										<AccordionContent>
											<div className="space-y-3 pt-2">
												{group.holidays && group.holidays.length > 0 ? (
													<div className="grid gap-3">
														{group.holidays.map((holiday) => {
															const holidayIcon = icons.find(
																(icon) => icon.value === holiday.icon
															);
															return (
																<div
																	key={holiday._id || holiday.id}
																	className="flex items-center justify-between p-3 rounded-lg border bg-card"
																>
																	<div className="flex items-center gap-3">
																		{holidayIcon ? (
																			<holidayIcon.icon className="h-4 w-4" />
																		) : (
																			<Pointer className="h-4 w-4" />
																		)}
																		<div>
																			<p className="font-medium text-sm">
																				{holiday.title || holiday.name}
																			</p>
																			<p className="text-xs text-muted-foreground">
																				{formatDate(
																					holiday.startDate || holiday.date
																				)}
																				{holiday.endDate &&
																					holiday.endDate !==
																						holiday.startDate && (
																						<>
																							{' '}
																							- {formatDate(holiday.endDate)}
																						</>
																					)}
																			</p>
																		</div>
																	</div>
																	<div className="flex items-center gap-2">
																		<Badge
																			variant="secondary"
																			className="text-xs bg-muted"
																		>
																			{holiday.numberOfDays}{' '}
																			{holiday.numberOfDays === 1
																				? 'day'
																				: 'days'}
																		</Badge>
																	</div>
																</div>
															);
														})}
													</div>
												) : (
													<div className="text-center py-4 text-muted-foreground">
														<p className="text-sm">
															No holidays defined for this group
														</p>
													</div>
												)}
											</div>
										</AccordionContent>
									</AccordionItem>
								))}
							</Accordion>
						)}
					</CardContent>
				</Card>

				{/* Leave Groups Card */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="flex items-center gap-2">
							<Clock className="h-5 w-5" />
							Leave Groups
						</CardTitle>
					</CardHeader>
					<CardContent>
						{!benefits?.leaveGroups || benefits.leaveGroups.length === 0 ? (
							<div className="text-center py-8 text-muted-foreground">
								<Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
								<p className="text-sm">No leave groups assigned</p>
								{isEditing && (
									<p className="text-xs mt-1">
										Contact HR to assign leave groups
									</p>
								)}
							</div>
						) : (
							<Accordion type="single" collapsible className="w-full">
								{benefits.leaveGroups.map((group) => (
									<AccordionItem
										key={group._id || group.id}
										value={`leave-${group._id || group.id}`}
									>
										<AccordionTrigger className="hover:no-underline">
											<div className="flex items-center justify-between w-full mr-4">
												<div className="flex items-center gap-3">
													<div className="flex items-center gap-2">
														<Users className="h-4 w-4 text-primary" />
														<span className="font-medium">{group.name}</span>
													</div>
													<div className="flex items-center gap-2">
														{group.totalDays && (
															<Badge variant="outline" className="text-xs">
																{group.remainingDays || group.totalDays}/
																{group.totalDays} days
																{group.remainingDays ? ' left' : ' total'}
															</Badge>
														)}
														{group.totalDays &&
															group.usedDays !== undefined && (
																<div className="w-16 h-2 bg-muted rounded-full overflow-hidden">
																	<div
																		className={cn(
																			'h-full transition-all duration-300',
																			getLeaveProgressColor(
																				(group.usedDays / group.totalDays) * 100
																			)
																		)}
																		style={{
																			width: `${Math.min((group.usedDays / group.totalDays) * 100, 100)}%`,
																		}}
																	/>
																</div>
															)}
													</div>
												</div>
												{group.description && (
													<p className="text-sm text-muted-foreground text-left">
														{group.description}
													</p>
												)}
											</div>
										</AccordionTrigger>
										<AccordionContent>
											<div className="space-y-3 pt-2">
												{group.leaveTypes && group.leaveTypes.length > 0 ? (
													<div className="grid gap-3">
														{group.leaveTypes.map((leaveType) => (
															<div
																key={leaveType._id || leaveType.id}
																className="flex items-center justify-between p-3 rounded-lg border bg-card"
															>
																<div className="flex items-center gap-3">
																	<MapPin className="h-4 w-4 text-muted-foreground" />
																	<div>
																		<p className="font-medium text-sm">
																			{leaveType.name || leaveType.type}
																		</p>
																		<p className="text-xs text-muted-foreground">
																			{leaveType.usedDays || 0} of{' '}
																			{leaveType.allowedDays ||
																				leaveType.totalDays ||
																				0}{' '}
																			days used
																		</p>
																	</div>
																</div>
																<div className="flex items-center gap-2">
																	{leaveType.allowedDays && (
																		<div className="w-20 h-2 bg-muted rounded-full overflow-hidden">
																			<div
																				className={cn(
																					'h-full transition-all duration-300',
																					getLeaveProgressColor(
																						((leaveType.usedDays || 0) /
																							leaveType.allowedDays) *
																							100
																					)
																				)}
																				style={{
																					width: `${Math.min(((leaveType.usedDays || 0) / leaveType.allowedDays) * 100, 100)}%`,
																				}}
																			/>
																		</div>
																	)}
																	{leaveType.carryForward && (
																		<Badge
																			variant="outline"
																			className="text-xs"
																		>
																			Carry Forward
																		</Badge>
																	)}
																</div>
															</div>
														))}
													</div>
												) : (
													<div className="text-center py-4 text-muted-foreground">
														<p className="text-sm">
															No leave types defined for this group
														</p>
													</div>
												)}
											</div>
										</AccordionContent>
									</AccordionItem>
								))}
							</Accordion>
						)}
					</CardContent>
				</Card>
			</div>
		</>
	);
};

export default EditEmployeeBenefitDetailsForm;
